#!/usr/bin/env python3
"""
启动脚本：设置正确的 Python 路径并启动 Chainlit 应用
"""
import os
import sys
import subprocess

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 添加项目根目录到 Python 路径
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置环境变量
os.environ['PYTHONPATH'] = project_root + os.pathsep + os.environ.get('PYTHONPATH', '')

# 启动 Chainlit 应用
if __name__ == "__main__":
    try:
        # 使用 subprocess 启动 chainlit，确保环境变量传递
        cmd = [sys.executable, "-m", "chainlit", "run", "ai_rag_system/ui.py", "--port", "8000"]
        subprocess.run(cmd, cwd=project_root, env=os.environ)
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")
