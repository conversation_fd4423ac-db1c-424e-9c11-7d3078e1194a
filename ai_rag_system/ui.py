import os
import sys
from typing import Optional, List

from chainlit.config import ThreadDict
from chainlit.input_widget import Select, Switch
from llama_index.core.base.llms.types import ChatMessage
from llama_index.core.memory import Chat<PERSON>emoryBuffer

from rag.base_rag import RAG
from rag.traditional_rag import TraditionalRAG

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

import chainlit as cl
import chainlit.data as cl_data
from chainlit.element import ElementBased
from llama_index.core import Settings,VectorStoreIndex,SimpleDirectoryReader

from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.chat_engine.types import ChatMode

from persistent.minio_storage_client import MinioStorageClient
from persistent.postgresql_data_layer import PostgreSQLDataLayer

# 实现聊天数据持久化
storage_client = MinioStorageClient()
cl_data._data_layer = PostgreSQLDataLayer(conninfo=os.environ["CONNECTION_STRING"], storage_provider=storage_client)

async def view_pdf(elements: List[ElementBased]):
    """查看PDF文件"""
    files = []
    contents = []
    for element in elements:
        if element.name.endswith(".pdf"):
            pdf = cl.Pdf(name=element.name, display="side", path=element.path, page=1)
            files.append(pdf)
            contents.append(element.name)
    if len(files) == 0:
        return
    await cl.Message(content=f"查看PDF文件：" + "，".join(contents), elements=files).send()

@cl.set_starters
async def set_starters():
    return [
        cl.Starter(
            label="大模型提高软件测试效率",
            message="详细介绍如何借助大语言模型提高软件测试效率。",
            icon="/public/apidog.svg",
            ),
        cl.Starter(
            label="自动化测试思路",
            message="详细描述一下接口及UI自动化测试的基本思路。",
            icon="/public/pulumi.svg",
            ),
        cl.Starter(
            label="性能测试分析及瓶颈定位思路",
            message="详细描述一下软件性能测试分析及瓶颈定位的核心思路。",
            icon="/public/godot_engine.svg",
            ),
        cl.Starter(
            label="如何学习大模型应用的核心技术",
            message="给出学习大语言模型的一些重要的技术和方法。",
            icon="/public/gleam.svg",
            )
        ]

@cl.on_chat_start
async def start():
    await cl.ChatSettings(
        [
            Select(
                id="Model",
                label="模型选择",
                values=["DeepSeek", "Moonshot", ],
                initial_index=0,
            ),
            Switch(id="multimodal", label="多模态RAG", initial=True),
        ]
    ).send()

    kb_name = cl.user_session.get("chat_profile")
    # 选择默认知识库，是与大模型直接对话
    if kb_name is None or kb_name == "default":
        memory = ChatMemoryBuffer.from_defaults(token_limit=1024)
        chat_engine = SimpleChatEngine.from_defaults(memory=memory)
    else:
        index = await RAG.load_index_remote(collection_name=kb_name)
        chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT)

    # 直接与大模型对话引擎
    # chat_engine = SimpleChatEngine.from_defaults()
    cl.user_session.set("chat_engine", chat_engine)

    # await cl.Message(
    #     author="Assistant", content="您好，我是但问智能助手，请问有什么可以帮到您的吗？"
    # ).send()


@cl.on_message
async def main(message: cl.Message):
    chat_engine = cl.user_session.get("chat_engine")
    msg = cl.Message(content="", author="Assistant")

    files = []
    pdf_elements = []

    # 获取用户上传的文件
    for element in message.elements:
        if isinstance(element, cl.File) or isinstance(element, cl.Image):
            files.append(element.path)
            # 收集PDF文件用于预览
            if element.name.endswith(".pdf"):
                pdf_elements.append(element)

    # 如果有PDF文件，显示PDF预览
    if pdf_elements:
        await view_pdf(pdf_elements)

    # 文件索引处理
    if len(files) > 0:
        rag = TraditionalRAG(files=files)
        # 构建索引，正常执行一次即可
        index = await rag.create_index_local()
        chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT)
        cl.user_session.set("chat_engine", chat_engine)

    res = await cl.make_async(chat_engine.stream_chat)(message.content)

    # 流式界面输出
    for token in res.response_gen:
        await msg.stream_token(token)
    await msg.send()

@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[cl.User]:
    # 可以对接第三方认证
    if (username, password) == ("admin", "admin"):
        return cl.User(identifier="admin",
                       metadata={"role": "admin", "provider": "credentials"})
    else:
        return None


@cl.on_settings_update
async def setup_settings(settings):
    cl.user_session.set("settings", settings)

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    chat_engine = SimpleChatEngine.from_defaults()
    for message in thread.get("steps", []):
        if message["type"] == "user_message":
            chat_engine.chat_history.append(ChatMessage(content=message["output"], role="user"))
        elif message["type"] == "assistant_message":
            chat_engine.chat_history.append(ChatMessage(content=message["output"], role="assistant"))

    cl.user_session.set("chat_engine", chat_engine)
