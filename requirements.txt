# Core Framework Dependencies
torch>=2.5.0,<2.8.0
openai>=1.97.0,<2.0.0
fastapi>=0.115.0,<0.116.0
uvicorn>=0.25.0,<0.26.0
starlette>=0.47.2,<0.48.0
httpx>=0.27.0,<0.29.0

# LlamaIndex Core and Extensions
llama-index-core>=0.12.0,<0.13.0
llama-index>=0.12.0,<0.13.0
llama-index-llms-openai>=0.4.0,<0.5.0
llama-index-embeddings-huggingface>=0.5.0,<0.6.0
llama-index-embeddings-openai>=0.3.0,<0.4.0
llama-index-vector-stores-milvus>=0.8.0,<0.9.0
llama-index-readers-file>=0.4.0,<0.5.0
llama-index-readers-llama-parse>=0.4.0,<0.5.0

# Chat Interface
chainlit>=1.3.0,<2.7.0

# Data Processing and Validation
pydantic>=2.9.0,<3.0.0
pydantic-settings>=2.10.0,<3.0.0

# Database and Storage
SQLAlchemy>=2.0.36,<2.1.0
asyncpg>=0.30.0,<0.31.0
pymilvus>=2.5.10,<3.0.0
minio>=7.2.0,<7.3.0

# File Processing and I/O
aiofiles>=23.2.0,<25.0.0
aiohttp>=3.11.0,<4.0.0
pillow>=10.4.0,<12.0.0
python-pptx>=1.0.0,<2.0.0
pypdf>=5.0.0,<6.0.0

# Environment and Configuration
python-dotenv>=1.0.0,<2.0.0

# Machine Learning and NLP
sentence-transformers>=5.0.0,<6.0.0
transformers>=4.45.0,<5.0.0
tokenizers>=0.20.0,<0.22.0
huggingface-hub>=0.26.0,<0.27.0
safetensors>=0.4.0,<0.6.0

# Async and Networking
httpcore>=1.0.0,<2.0.0
anyio>=4.9.0,<5.0.0
sniffio>=1.3.0,<2.0.0

# Utilities
tqdm>=4.66.0,<5.0.0
tenacity>=9.1.0,<10.0.0
click>=8.1.0,<9.0.0
typing-extensions>=4.14.0,<5.0.0

# Additional Document Processing
lxml>=6.0.0,<7.0.0
xlsxwriter>=3.2.0,<4.0.0

# Development and Monitoring (Optional)
# Uncomment if needed for development
# jupyter>=1.1.0,<2.0.0
# notebook>=7.2.0,<8.0.0

# Production Dependencies (Optional)
# Uncomment for production deployment
# gunicorn>=21.0.0,<22.0.0
# redis>=5.0.0,<6.0.0