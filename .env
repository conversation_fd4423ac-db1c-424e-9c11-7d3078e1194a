# chainlit create-secret
CHAINLIT_AUTH_SECRET="hj%J?i*=po7SA_/XW_H^6X@4_hVeGzjct0rM5xbfmkJ^$$,K.*u=.wq,o9L%9Rc6"

# LLM Platform Configurations
# Gitee AI Platform
GITEE_API_KEY="RWXIXI17C22UTQJEMVCI2AZEWGVOPE493Q2GM31A"
#GITEE_API_BASE="https://ai.gitee.com/v1"

# FastGPT Platform
FASTGPT_API_KEY="fastgpt-rFP76EYk5wkZ1S9va6n90mhT7ragq777EmXZ8TaanGQCicZuZcjbpxmXGZC5tg4Zv"
#FASTGPT_API_BASE="https://api.fastgpt.in/api/v1"

# Moonshot Platform
MOONSHOT_API_KEY="sk-dONPz6CqbSzNHMGX6fhwUaSqrWi9RHRkp1XAx51acXwPhnNk"
#MOONSHOT_API_BASE="https://api.moonshot.cn/v1"
# kimi的文件解析服务 https://platform.moonshot.cn/docs/api/files#%E4%B8%8A%E4%BC%A0%E6%96%87%E4%BB%B6

# MinIO Configuration
MINIO_ENDPOINT="114.55.110.60:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_BUCKET_NAME="005"
#代码访问是9000，浏览器访问是9001
# MinIO 社区版 默认只有一组“根凭证”，即启动时通过环境变量指定的：
# MINIO的 Access Key和 Secret Key，默认是：minioadmin/minioadmin
# MinIO 提供了一个强大的命令行工具 mc（MinIO Client），可以通过它来修改桶的访问权限。

# Database Configuration
CONNECTION_STRING="postgresql+asyncpg://postgres:rxd123rxd@localhost/chainlit_db"

# Vector Database Configuration
MILVUS_URI="http://**************:19530"

