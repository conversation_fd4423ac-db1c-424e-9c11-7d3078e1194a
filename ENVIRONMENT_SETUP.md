# 项目环境配置说明

## 项目概述

这是一个基于 LlamaIndex 和 Chainlit 的智能 RAG (检索增强生成) 系统，支持多种大语言模型和向量数据库。

## 系统架构

- **前端界面**: Chainlit Web UI
- **RAG 框架**: LlamaIndex
- **向量数据库**: Milvus
- **关系数据库**: PostgreSQL (聊天记录持久化)
- **对象存储**: MinIO (文件存储)
- **嵌入模型**: HuggingFace BGE-small-zh-v1.5 (中文)
- **大语言模型**: 
  - DeepSeek-V3 (通过 Gitee AI)
  - Kimi-k2-instruct (通过 Gitee AI)
  - Claude-3.5-Hai<PERSON> (通过 FastGPT)
  - Moonshot (直接接入)

## 环境要求

- Python 3.12+
- Windows/Linux/macOS
- 至少 8GB RAM (推荐 16GB+)
- 网络连接 (用于模型下载和 API 调用)

## 安装步骤

### 1. 创建虚拟环境 (推荐)

```bash
# 使用 conda
conda create -n rag_system python=3.12
conda activate rag_system

# 或使用 venv
python -m venv rag_env
# Windows
rag_env\Scripts\activate
# Linux/macOS
source rag_env/bin/activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 环境变量配置

复制 `.env.example` 到 `.env` 并配置以下变量：

```env
# Chainlit 认证密钥
CHAINLIT_AUTH_SECRET="your_secret_key"

# LLM API 配置
GITEE_API_KEY="your_gitee_api_key"
FASTGPT_API_KEY="your_fastgpt_api_key"
MOONSHOT_API_KEY="your_moonshot_api_key"

# MinIO 配置
MINIO_ENDPOINT="your_minio_endpoint"
MINIO_ACCESS_KEY="your_access_key"
MINIO_SECRET_KEY="your_secret_key"
MINIO_BUCKET_NAME="your_bucket_name"

# 数据库配置
CONNECTION_STRING="postgresql+asyncpg://user:password@host/database"

# 向量数据库配置
MILVUS_URI="http://your_milvus_host:19530"
```

## 核心依赖说明

### 框架核心
- **torch**: 深度学习框架，支持模型推理
- **openai**: OpenAI 兼容的 API 客户端
- **fastapi**: 高性能 Web 框架
- **chainlit**: 聊天界面框架

### LlamaIndex 生态
- **llama-index-core**: 核心 RAG 框架
- **llama-index-llms-openai**: OpenAI 兼容的 LLM 接口
- **llama-index-embeddings-huggingface**: HuggingFace 嵌入模型支持
- **llama-index-vector-stores-milvus**: Milvus 向量存储集成
- **llama-index-readers-file**: 文件读取器

### 数据处理
- **pydantic**: 数据验证和序列化
- **SQLAlchemy**: ORM 框架
- **asyncpg**: PostgreSQL 异步驱动
- **pymilvus**: Milvus 客户端
- **minio**: MinIO 对象存储客户端

### 文档处理
- **pillow**: 图像处理
- **pypdf**: PDF 处理
- **python-pptx**: PowerPoint 处理
- **lxml**: XML/HTML 处理
- **xlsxwriter**: Excel 文件写入

### 机器学习
- **sentence-transformers**: 句子嵌入模型
- **transformers**: HuggingFace 模型库
- **huggingface-hub**: 模型下载和管理

## 启动应用

```bash
# 进入应用目录
cd ai_rag_system

# 启动 Chainlit 应用
chainlit run ui.py -w
```

## 功能特性

1. **多模态 RAG**: 支持文本、图片、PDF 等多种文件格式
2. **多模型支持**: 可切换不同的大语言模型
3. **聊天记录持久化**: 基于 PostgreSQL 的聊天历史存储
4. **文件预览**: PDF 文件在线预览功能
5. **用户认证**: 简单的用户名密码认证
6. **OCR 功能**: 通过 Moonshot API 进行文件内容提取

## 故障排除

### 常见问题

1. **依赖冲突**: 确保使用最新的 requirements.txt
2. **模型下载失败**: 检查网络连接和 HuggingFace 访问
3. **数据库连接失败**: 验证 PostgreSQL 和 Milvus 服务状态
4. **API 调用失败**: 检查 API 密钥和网络连接

### 性能优化

1. **GPU 加速**: 如有 NVIDIA GPU，安装 CUDA 版本的 PyTorch
2. **内存优化**: 调整批处理大小和缓存设置
3. **并发处理**: 配置适当的异步并发数

## 更新说明

当前 requirements.txt 已优化：
- 修复了 pymilvus 版本冲突问题
- 添加了所有必需的 LlamaIndex 扩展
- 包含了完整的文档处理库
- 使用了兼容的版本约束

## 技术支持

如遇到问题，请检查：
1. Python 版本是否为 3.12+
2. 所有环境变量是否正确配置
3. 外部服务 (Milvus, PostgreSQL, MinIO) 是否正常运行
4. 网络连接是否稳定
